// out: false
.tourBlock {
    &.inview {
        .tourList {
            li {
                &:nth-child(-n+6) {
                    opacity: 1;
                    .transform(translateY(0));
                    .stacker(100, .15s);
                    transition: opacity .45s ease-in-out, transform .45s cubic-bezier(0.34, 1.56, 0.64, 1);
                }
            }
        }
    }
    &.allEvents {
        .tourList {
            li {
                display: block;
            }
        }
        .button {
            display: none;
        }
    }
    .col {
        display: inline-block;
        vertical-align: top;
        width: 40%;
        &:first-child {
            padding-top: @vw22;
            padding-right: (@vw112 + @vw16 + @vw16);
            padding-left: (@vw112 + @vw16);
            width: 60%;
        }
    }
    .normalTitle {
        margin-bottom: @vw50;
    }
    .imageWrapper {
        height: auto;
        width: 100%;
        position: relative;
        overflow: hidden;
        .rounded(@vw40);
        .innerImage {
            height: 0;
            width: 100%;
            .paddingRatio(624,863);
            img, video {
                position: absolute;
                top: 0;
                left: -20%;
                width: 140%;
                height: 100%;
                pointer-events: none;
                object-fit: cover;
                object-position: center;
            }
        }
        
    }
    .tourList {
        list-style: none;
        line-height: 1;
        li {
            position: relative;
            padding: @vw22 0;
            white-space: nowrap;
            display: none;
            &:nth-child(-n+6) {
                display: block;
                opacity: 0;
                .transform(translateY(@vw50));
            }
            &:after {
                content: '';
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 1px; 
                background: rgba(255,255,255,.1);
            }
            .smallTitle {
                display: block;
                margin-bottom: @vw16;
                font-size: @vw16;
            }
            .innerCol {
                display: inline-block;
                width: 50%;
                vertical-align: top;
                &:last-child {
                    text-align: right;
                }
            }
        }
    }
    .button {
        margin-top: @vw30;
        display: block;
        width: 100%;
        text-align: center;
    }
}

@media all and (max-width: 1160px) {
    .tourBlock {
        .col {
            &:first-child {
                padding-top: 0;
                padding-right: @vw60-1160;
                padding-left: 0;
            }
        }
        .normalTitle {
            margin-bottom: @vw30-1160;
        }
        .imageWrapper {
            .rounded(@vw40-1160);
            .innerImage {
                .paddingRatio(600,1150);
            }
        }
        .tourList {
            li {
                padding: @vw22-1160 0;
                &:nth-child(-n+6) {
                    .transform(translateY(@vw50-1160));
                }
                .smallTitle {
                    margin-bottom: @vw16-1160;
                    font-size: @vw16-1160;
                }
            }
        }
        .button {
            margin-top: @vw30-1160;
        }
    }
}

@media all and (max-width: 580px) {
    .tourBlock {
        .col {
            width: 100%;
            &:first-child {
                padding-top: 0;
                padding-right: 0;
                padding-left: 0;
                position: relative;
                z-index: 1;
                width: 100%;
            }
            &.image {
                position: absolute;
                left: -@vw20-580;
                top: -@vw120-580;
                width: calc(100% ~"+" @vw40-580);
                height: calc(100% ~"+" @vw120-580 ~"+" @vw120-580);
                .rounded(@vw40-580);
                z-index: 0;
            }
        }
        .normalTitle {
            margin-bottom: @vw50-580;
            text-align: center;
        }
        .imageWrapper {
            .rounded(@vw40-580);
            height: 100%;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            .innerImage {
                padding-bottom: 0;
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
                filter: blur(10px);
            }
        }
        .tourList {
            li {
                padding: @vw22-580 0;
                &:nth-child(-n+6) {
                    .transform(translateY(@vw50-580));
                }
                .smallTitle {
                    margin-bottom: @vw16-580;
                    font-size: @vw16-580;
                }
            }
        }
        .button {
            margin-top: @vw30-580;
        }
    }
}