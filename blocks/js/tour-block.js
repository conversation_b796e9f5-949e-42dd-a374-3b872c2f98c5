$(document).ready(function(){
    $(document).on("initPage", function() {
      setTourBlock();
    });
  });

  function setTourBlock() {
    $(document).on("click", ".tourBlock .button", function(){
      $(this).parents(".tourBlock").addClass("allEvents");
    });

    // Start automatische updates elke 5 minuten
    startTourAutoUpdate();
  }

  function startTourAutoUpdate() {
    // Update elke 5 minuten (300000 ms)
    setInterval(function() {
      refreshTourData();
    }, 300000);

    // Ook een update na 30 seconden voor snelle feedback
    setTimeout(function() {
      refreshTourData();
    }, 30000);
  }

  function refreshTourData() {
    if (typeof ajax_url === 'undefined') {
      return;
    }

    $.ajax({
      url: ajax_url,
      type: 'POST',
      data: {
        action: 'refresh_tour_data'
      },
      success: function(response) {
        if (response.success && response.data) {
          updateTourList(response.data);
        }
      },
      error: function() {
        console.log('Tour data refresh failed');
      }
    });
  }

  function updateTourList(tourData) {
    $('.tourBlock .tourList').each(function() {
      var $tourList = $(this);
      var $tourBlock = $tourList.closest('.tourBlock');

      if (tourData.length === 0) {
        // Geen events, toon message
        var message = $tourBlock.find('.tour').data('message') || 'Geen aankomende events';
        $tourList.parent().html('<p>' + message + '</p>');
        return;
      }

      // Bouw nieuwe lijst op
      var newHtml = '';
      $.each(tourData, function(index, event) {
        newHtml += '<li>';
        newHtml += '<span class="smallTitle">' + event.date + '</span>';
        newHtml += '<span class="innerCol">' + event.title + '</span>';
        newHtml += '<span class="innerCol">' + event.location + '</span>';
        newHtml += '</li>';
      });

      // Update de lijst met fade effect
      $tourList.fadeOut(300, function() {
        $tourList.html(newHtml).fadeIn(300);

        // Trigger inview animatie opnieuw als nodig
        if ($tourBlock.hasClass('inview')) {
          $tourBlock.removeClass('inview');
          setTimeout(function() {
            $tourBlock.addClass('inview');
          }, 100);
        }
      });
    });
  }