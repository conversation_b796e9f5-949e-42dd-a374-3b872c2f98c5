<?php

function jn_enqueue_assets() {
    $scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'FLICKITY' => '/libs/flickity.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'Split text' => '/libs/SplitText.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Hammer_js' => '/libs/hammer.min.js',
        'main_js' => '/assets/js/main.js',
        'Header' => '/assets/js/header.js',
        'Confetti' => '/assets/js/parts/confetti.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Inview' => '/assets/js/parts/inview.js',
    );

    foreach ($scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    $blocks = array(
        'HEADER BLOCK' => '/blocks/js/header-block.js',
        'TOUR BLOCK' => '/blocks/js/tour-block.js',
    );

    foreach ($blocks as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    // wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-spotify' => 'Spotify URL',
        'customTheme-main-callout-youtube' => 'Youtube URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        $control_args = array(
            'label' => $label,
            'section' => 'customTheme-main-callout-section',
            'settings' => $setting_id
        );

        if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false) {
            $control_args['width'] = 750;
            $control_args['height'] = 500;
            $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
        } elseif ($label === 'Description') {
            $control_args['type'] = 'textarea';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        } else {
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'primary-menu' => 'Primary Menu'
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks
add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'header_block',
            'title'             => __('Header Block'),
            'render_template'   => 'blocks/header-block.php',
            'category'          => 'header',
        ));
        acf_register_block_type(array(
            'name'              => 'quote_block',
            'title'             => __('Quote Block'),
            'render_template'   => 'blocks/quote-block.php',
            'category'          => 'quote',
        ));
        acf_register_block_type(array(
            'name'              => 'tour_block',
            'title'             => __('Tour Block'),
            'render_template'   => 'blocks/tour-block.php',
            'category'          => 'tour',
        ));
        acf_register_block_type(array(
            'name'              => 'slider_block',
            'title'             => __('Slider Block'),
            'render_template'   => 'blocks/slider-block.php',
            'category'          => 'slider',
        ));
        acf_register_block_type(array(
            'name'              => 'cta_block',
            'title'             => __('CTA Block'),
            'render_template'   => 'blocks/cta-block.php',
            'category'          => 'cta',
        ));
    }
}

// Custom Post Type for Tour Data
function register_tour_data_post_type() {
    register_post_type('tour_data', [
        'labels' => [
            'name' => __('Tour Data'),
            'singular_name' => __('Tour Data'),
        ],
        'public' => true,
        'has_archive' => true,
        'supports' => ['title', 'editor'],
    ]);
}
add_action('init', 'register_tour_data_post_type');

function fetch_google_calendar_events() {
    // Dit is je originele cronjob functie
    error_log('Cronjob is uitgevoerd om ' . date('Y-m-d H:i:s'));

    $api_key = 'AIzaSyCGTZY-pFPArD71z3a7WJfVSoty5Ci-4Kg'; // Vervang met je eigen API-sleutel.
    $calendar_id = '<EMAIL>'; // Je kalender-ID.
    $url = sprintf(
        'https://www.googleapis.com/calendar/v3/calendars/%s/events?key=%s',
        urlencode($calendar_id),
        $api_key
    );

    $response = wp_remote_get($url);
    if (is_wp_error($response)) {
        return;
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (!empty($data['items'])) {
        foreach ($data['items'] as $event) {
            save_event_as_post($event);
        }
    }
}


function save_event_as_post($event) {
    $event_name = $event['summary'];
    $event_date = $event['start']['dateTime'] ?? $event['start']['date'];
    $event_location = $event['location'] ?? 'Online';

    // Controleer of dit evenement al is opgeslagen.
    $existing_posts = get_posts([
        'post_type' => 'tour_data',
        'meta_key' => 'event_id',
        'meta_value' => $event['id'],
    ]);

    if ($existing_posts) {
        return; // Sla over als het al bestaat.
    }

    // Maak een nieuwe post.
    $post_id = wp_insert_post([
        'post_title'   => $event_name,
        'post_type'    => 'tour_data',
        'post_status'  => 'publish',
    ]);

    // Voeg ACF-velden toe.
    if ($post_id) {
        update_field('event_id', $event['id'], $post_id);
        update_field('event_date', $event_date, $post_id);
        update_field('event_location', $event_location, $post_id);
    }
}

// Voeg een custom interval toe voor 5 minuten.
function custom_cron_schedule($schedules) {
    // Stel een kortere interval in voor testen
    $schedules['every_minute'] = array(
        'interval' => 3600, // 3600 seconden = 1 uur
        'display'  => __('Every Hour')
    );
    return $schedules;
}
add_filter('cron_schedules', 'custom_cron_schedule');


// Cronjob instellen
function schedule_google_calendar_sync() {
    if (!wp_next_scheduled('sync_google_calendar_events')) {
        wp_schedule_event(time(), 'every_five_minutes', 'sync_google_calendar_events');
    }
}
add_action('wp', 'schedule_google_calendar_sync');

add_action('sync_google_calendar_events', 'fetch_google_calendar_events');

?>
